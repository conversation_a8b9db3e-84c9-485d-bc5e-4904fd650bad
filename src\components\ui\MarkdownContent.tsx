'use client';

import React, { useEffect, useRef } from 'react';

interface MarkdownContentProps {
  content: string;
  className?: string;
}

export function MarkdownContent({ content, className = '' }: MarkdownContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      // 为所有图片添加alt文本显示
      const images = contentRef.current.querySelectorAll('img[alt]');
      images.forEach((img) => {
        const altText = img.getAttribute('alt');
        if (altText && !img.nextElementSibling?.classList.contains('image-caption')) {
          const caption = document.createElement('div');
          caption.className = 'image-caption text-center text-sm text-gray-600 dark:text-gray-400 mt-3 mb-6 italic';
          caption.textContent = altText;
          img.parentNode?.insertBefore(caption, img.nextSibling);
        }
      });
    }
  }, [content]);

  return (
    <div
      ref={contentRef}
      className={`markdown-content ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
      style={{
        lineHeight: '1.7',
        color: '#374151'
      }}
    />
  );
}
