import { ServicesList } from '@/components/sections/ServicesList';
import { QQContactButton } from '@/components/ui/QQContactButton';
import { StructuredData } from '@/components/seo/StructuredData';

export const metadata = {
  title: "三更订阅库服务列表 - 经济学人、华尔街日报等6大媒体永久订阅",
  description: "三更订阅库提供经济学人(¥1100)、华尔街日报(¥380)、金融时报(¥280)、日经亚洲(¥400)、纽约时报(¥400)、华盛顿邮报(¥400)永久使用权订阅。充值独享账号，官网直接登录，三更订阅库专业技术保障。",
  alternates: {
    canonical: "https://sgtushu.com/services",
  },
  openGraph: {
    title: "三更订阅库服务列表 - 6大顶级媒体永久订阅",
    description: "三更订阅库提供经济学人、华尔街日报、金融时报、日经亚洲、纽约时报、华盛顿邮报永久使用权订阅服务",
    url: "https://sgtushu.com/services",
    siteName: "三更订阅库",
    locale: "zh_CN",
    type: "website",
    images: [
      {
        url: "https://sgtushu.com/sgtushu-image.jpeg",
        width: 1200,
        height: 630,
        alt: "三更订阅库服务列表 - 6大顶级媒体永久订阅",
      },
    ],
  },
};

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <StructuredData type="service" />
      <ServicesList />
      <QQContactButton />
    </div>
  );
}
