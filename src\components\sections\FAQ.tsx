'use client';

import React, { useState } from 'react';
import { ContactButton } from '../ui/ContactButton';
import { Button } from '../ui/Button';

const faqData = [
  {
    id: 1,
    question: '什么是永久使用权？',
    answer: '永久使用权意味着您一次购买后可以终身使用该媒体服务。三更订阅库为您提供充值独享账号，您可以直接在官网登录使用，无需担心账号到期问题。'
  },
  {
    id: 2,
    question: '如何保证账号的安全性？',
    answer: '三更订阅库采用专业的技术保障体系，所有账号信息都经过加密处理。我们提供的是充值独享账号，确保您的使用安全，并且有专业技术团队24/7监控服务状态。'
  },
  {
    id: 3,
    question: '购买后多久可以使用？',
    answer: '三更订阅库提供即时激活服务。付款完成后，我们会立即为您开通账号并提供登录信息，通常在几分钟内即可开始使用。'
  },
  {
    id: 4,
    question: '可以在多个设备上使用吗？',
    answer: '是的，您购买的永久使用权支持在多个设备上登录使用。具体的设备数量限制以各媒体官方政策为准，我们的客服会为您详细说明。'
  },
  {
    id: 5,
    question: '如果账号出现问题怎么办？',
    answer: '三更订阅库提供全程技术支持保障。如果账号出现任何问题，请立即联系我们的客服团队，我们会第一时间为您解决，确保您的使用体验。'
  },
  {
    id: 6,
    question: '价格包含哪些服务？',
    answer: '价格包含永久使用权、充值独享账号、官网直接登录体验、三更订阅库技术支持、即时激活服务等。价格透明，无任何隐藏费用。'
  },
  {
    id: 7,
    question: '支持哪些支付方式？',
    answer: '三更订阅库支持多种安全的支付方式，包括微信支付、支付宝、银行卡等。具体支付方式请咨询客服，我们会为您提供最便捷的支付选择。'
  },
  {
    id: 8,
    question: '与官方订阅有什么区别？',
    answer: '三更订阅库提供的是永久使用权，而官方通常是按月或按年订阅。我们的价格更优惠，服务更灵活，并且提供专业的中文客服支持。'
  }
];

export const FAQ: React.FC = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section id="faq" className="py-12 sm:py-16 lg:py-24 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6 tracking-tight">
            常见问题
          </h2>
          <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
            关于三更订阅库服务的常见问题解答，帮助您更好地了解我们的服务
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqData.map((item) => (
            <div
              key={item.id}
              className="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full px-4 sm:px-6 py-4 sm:py-5 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white pr-4">
                  {item.question}
                </h3>
                <div className={`flex-shrink-0 transition-transform duration-200 ${
                  openItems.includes(item.id) ? 'rotate-180' : ''
                }`}>
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              
              {openItems.includes(item.id) && (
                <div className="px-4 sm:px-6 pb-4 sm:pb-5">
                  <div className="pt-2 border-t border-gray-100 dark:border-gray-700">
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            还有其他问题？我们的专业客服团队随时为您解答
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <ContactButton variant="primary" size="lg">
              联系客服
            </ContactButton>
            <Button variant="outline" size="lg" href="/services">
              查看更多服务
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
