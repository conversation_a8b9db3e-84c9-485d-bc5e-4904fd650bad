import React from 'react';
import Image from 'next/image';
import { Card, CardHeader, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';
import { ContactButton } from '../ui/ContactButton';

const services = [
  {
    id: 1,
    name: '经济学人 The Economist',
    description: '全球权威的政经周刊，深度分析国际时事和经济趋势。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥1100',
    period: '永久',
    image: '/images/services/sangengtushu-economist-subscription-service.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  },
  {
    id: 2,
    name: '华尔街日报 WSJ',
    description: '美国最具影响力的财经报纸，提供最新的商业和金融资讯。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥380',
    period: '永久',
    image: '/images/services/sangengtushu-wsj-wall-street-journal-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  },
  {
    id: 3,
    name: '金融时报 FT',
    description: '英国权威财经报纸，全球商业和金融领域的领导者。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥280',
    period: '永久',
    image: '/images/services/sangengtushu-ft-financial-times-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  },
  {
    id: 4,
    name: '日经亚洲 Nikkei Asia',
    description: '日本经济新闻旗下英文媒体，专注亚洲商业和经济报道。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    image: '/images/services/sangengtushu-nikkei-asia-subscription-service.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  },
  {
    id: 5,
    name: '纽约时报 NYT',
    description: '美国权威报纸，提供全球新闻、深度报道和分析。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    image: '/images/services/sangengtushu-nyt-new-york-times-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  },
  {
    id: 6,
    name: '华盛顿邮报 Washington Post',
    description: '美国重要报纸，提供政治、社会和国际新闻报道。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    image: '/images/services/sangengtushu-washington-post-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持']
  }
];

export const Services: React.FC = () => {
  return (
    <section id="services" className="py-12 sm:py-16 lg:py-24 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6 tracking-tight">
            三更订阅库订阅服务
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            三更订阅库通过专业技术平台，为您提供多种优质内容订阅服务，
            享受比官方更优惠的价格和更稳定的服务体验。
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {services.slice(0, 4).map((service) => (
            <Card key={service.id} className="h-full flex flex-col">
              <CardHeader className="p-0">
                <div className="relative w-full h-48 rounded-t-xl overflow-hidden">
                  <Image
                    src={service.image}
                    alt={`${service.name} - 三更订阅库订阅服务`}
                    fill
                    className="object-cover"
                  />
                </div>
              </CardHeader>

              <CardContent className="flex-grow p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 tracking-tight">
                  {service.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 leading-relaxed">
                  {service.description}
                </p>

                <div className="mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                      {service.price}
                    </span>
                  </div>
                  <div className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                    三更订阅库特惠价格
                  </div>
                </div>

                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <span className="w-1.5 h-1.5 bg-teal-600 rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>

              <CardFooter className="p-6 pt-0">
                <ContactButton variant="primary" className="w-full">
                  咨询客服
                </ContactButton>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="mb-8">
            <Button variant="outline" size="lg" href="/services">
              查看全部订阅服务
            </Button>
          </div>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            所有服务均由三更订阅库技术平台提供支持，确保订阅安全和服务稳定性
          </p>
        </div>
      </div>
    </section>
  );
};
