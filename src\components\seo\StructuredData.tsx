import Script from 'next/script'

interface StructuredDataProps {
  type?: 'website' | 'organization' | 'service'
}

export const StructuredData: React.FC<StructuredDataProps> = ({ type = 'website' }) => {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === 'organization' ? "Organization" : "WebSite",
      "name": "三更订阅库",
      "alternateName": "sgtushu",
      "url": "https://sgtushu.com",
      "logo": "https://sgtushu.com/sgtushu-logo.png",
      "description": "三更订阅库提供经济学人、华尔街日报、金融时报、日经亚洲、纽约时报、华盛顿邮报等全球顶级媒体的永久使用权订阅服务",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": "Chinese"
      }
    }

    if (type === 'website') {
      return {
        ...baseData,
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://sgtushu.com/services?search={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    }

    if (type === 'service') {
      return {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "知识订阅服务",
        "provider": {
          "@type": "Organization",
          "name": "三更订阅库",
          "url": "https://sgtushu.com"
        },
        "serviceType": "数字内容订阅",
        "description": "提供经济学人、华尔街日报等全球顶级媒体的永久使用权订阅服务",
        "offers": [
          {
            "@type": "Offer",
            "name": "经济学人永久订阅",
            "price": "1100",
            "priceCurrency": "CNY",
            "description": "经济学人永久使用权，充值独享账号"
          },
          {
            "@type": "Offer", 
            "name": "华尔街日报永久订阅",
            "price": "380",
            "priceCurrency": "CNY",
            "description": "华尔街日报永久使用权，充值独享账号"
          }
        ]
      }
    }

    return baseData
  }

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData()),
      }}
    />
  )
}
