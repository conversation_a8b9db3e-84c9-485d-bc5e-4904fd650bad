import React from 'react';
import Image from 'next/image';
import { But<PERSON> } from '../ui/Button';
import { ContactButton } from '../ui/ContactButton';

export const Hero: React.FC = () => {
  return (
    <section className="relative bg-gradient-to-br from-gray-50 to-teal-50/30 dark:from-gray-900 dark:to-gray-800 py-12 sm:py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
          {/* Left Content - 50% */}
          <div className="text-center lg:text-left">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6 tracking-tight leading-tight">
              三更订阅库
              <span className="block text-teal-600 dark:text-teal-400 text-2xl sm:text-3xl lg:text-4xl xl:text-5xl mt-1 sm:mt-2">
                专业订阅服务
              </span>
            </h1>
            <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 leading-relaxed max-w-2xl lg:max-w-none">
              三更订阅库是专业的知识订阅平台，为您提供经济学人、华尔街日报、朝日新闻、Cloze Master 等顶级内容的低价会员服务。
              三更订阅库技术保障，安全可靠的订阅管理体验。
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start mb-6 sm:mb-8">
              <ContactButton variant="primary" size="lg">
                咨询客服
              </ContactButton>
              <Button variant="outline" size="lg" href="/services">
                查看全部服务
              </Button>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>✓ 三更订阅库数据安全保障 ✓ 24/7 客户支持 ✓ 即时激活服务</p>
            </div>
          </div>

          {/* Right Content - Hero Image - 50% */}
          <div className="relative">
            {/* 3:2 比例的图片容器 */}
            <div className="relative w-full aspect-[3/2] rounded-2xl overflow-hidden shadow-lg">
              <Image
                src="/images/services/sangengtushu-nyt-new-york-times-subscription.png"
                alt="三更订阅库 - nocturnedb 订阅服务平台"
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
