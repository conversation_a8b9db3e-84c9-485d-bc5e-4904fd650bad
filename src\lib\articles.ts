import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import remarkGfm from 'remark-gfm';

const articlesDirectory = path.join(process.cwd(), 'src/content/articles');

export interface ArticleMetadata {
  slug: string;
  title: string;
  description: string;
  date: string;
  category: string;
  tags: string[];
  author?: string;
  featured?: boolean;
  readTime?: number;
}

export interface Article extends ArticleMetadata {
  content: string;
}

// 获取所有文章的元数据
export function getAllArticles(): ArticleMetadata[] {
  if (!fs.existsSync(articlesDirectory)) {
    return [];
  }

  const fileNames = fs.readdirSync(articlesDirectory);
  const articles = fileNames
    .filter(name => name.endsWith('.md'))
    .map(name => {
      const slug = name.replace(/\.md$/, '');
      const fullPath = path.join(articlesDirectory, name);
      const fileContents = fs.readFileSync(fullPath, 'utf8');
      const { data } = matter(fileContents);

      return {
        slug,
        title: data.title || '',
        description: data.description || '',
        date: data.date || '',
        category: data.category || 'general',
        tags: data.tags || [],
        author: data.author,
        featured: data.featured || false,
        readTime: data.readTime || calculateReadTime(fileContents),
      } as ArticleMetadata;
    })
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  return articles;
}

// 根据slug获取单篇文章
export async function getArticleBySlug(slug: string): Promise<Article | null> {
  try {
    const fullPath = path.join(articlesDirectory, `${slug}.md`);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);

    // 将Markdown转换为HTML
    const processedContent = await remark()
      .use(remarkGfm)
      .use(html)
      .process(content);

    return {
      slug,
      title: data.title || '',
      description: data.description || '',
      date: data.date || '',
      category: data.category || 'general',
      tags: data.tags || [],
      author: data.author,
      featured: data.featured || false,
      readTime: data.readTime || calculateReadTime(content),
      content: processedContent.toString(),
    };
  } catch (error) {
    console.error(`Error reading article ${slug}:`, error);
    return null;
  }
}

// 根据分类获取文章
export function getArticlesByCategory(category: string): ArticleMetadata[] {
  const articles = getAllArticles();
  return articles.filter(article => article.category === category);
}

// 获取所有分类
export function getAllCategories(): string[] {
  const articles = getAllArticles();
  const categories = [...new Set(articles.map(article => article.category))];
  return categories.sort();
}

// 搜索文章
export function searchArticles(query: string): ArticleMetadata[] {
  const articles = getAllArticles();
  const lowercaseQuery = query.toLowerCase();
  
  return articles.filter(article => 
    article.title.toLowerCase().includes(lowercaseQuery) ||
    article.description.toLowerCase().includes(lowercaseQuery) ||
    article.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}

// 计算阅读时间（基于平均阅读速度200字/分钟）
function calculateReadTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

// 获取相关文章
export function getRelatedArticles(currentSlug: string, limit: number = 3): ArticleMetadata[] {
  const articles = getAllArticles();
  const currentArticle = articles.find(article => article.slug === currentSlug);
  
  if (!currentArticle) return [];

  // 基于分类和标签的相关性算法
  const relatedArticles = articles
    .filter(article => article.slug !== currentSlug)
    .map(article => {
      let score = 0;
      
      // 同分类加分
      if (article.category === currentArticle.category) {
        score += 3;
      }
      
      // 共同标签加分
      const commonTags = article.tags.filter(tag => 
        currentArticle.tags.includes(tag)
      );
      score += commonTags.length * 2;
      
      return { ...article, score };
    })
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  return relatedArticles;
}
