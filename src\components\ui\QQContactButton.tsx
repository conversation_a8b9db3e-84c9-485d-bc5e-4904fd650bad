'use client';

import React, { useState } from 'react';

export const QQContactButton: React.FC = () => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const qqNumber = '1374783452';

  const handleCopyQQ = async () => {
    try {
      await navigator.clipboard.writeText(qqNumber);
      setCopied(true);
      setShowTooltip(true);
      
      // 3秒后隐藏提示
      setTimeout(() => {
        setCopied(false);
        setShowTooltip(false);
      }, 3000);
    } catch {
      // 如果clipboard API不可用，使用fallback方法
      const textArea = document.createElement('textarea');
      textArea.value = qqNumber;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      setCopied(true);
      setShowTooltip(true);
      setTimeout(() => {
        setCopied(false);
        setShowTooltip(false);
      }, 3000);
    }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-16 right-0 mb-2 px-4 py-3 bg-gray-900 dark:bg-gray-800 text-white text-sm rounded-lg shadow-lg whitespace-nowrap border border-gray-700">
          {copied ? (
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>QQ号已复制: {qqNumber}</span>
            </div>
          ) : (
            <span>点击复制QQ号: {qqNumber}</span>
          )}
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-800"></div>
        </div>
      )}
      
      {/* QQ Button */}
      <button
        onClick={handleCopyQQ}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => !copied && setShowTooltip(false)}
        className="relative w-14 h-14 bg-teal-600 text-white rounded-full shadow-lg flex items-center justify-center border-2 border-teal-500"
        title="联系客服"
      >
        {/* QQ Icon */}
        <svg
          className="w-7 h-7"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        
        {/* Pulse animation when copied */}
        {copied && (
          <div className="absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-75"></div>
        )}
      </button>
      
      {/* Contact text */}
      <div className="absolute bottom-2 right-16 text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap font-medium">
        三更知识库客服
      </div>
    </div>
  );
};
