import type { Metadata } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "三更订阅库 - 专业知识订阅服务平台 | 经济学人、华尔街日报永久订阅",
  description: "三更订阅库提供经济学人、华尔街日报、金融时报、日经亚洲、纽约时报、华盛顿邮报等全球顶级媒体的永久使用权订阅服务。充值独享账号，官网直接登录，三更订阅库专业技术保障。",
  authors: [{ name: "三更订阅库" }],
  creator: "三更订阅库",
  publisher: "三更订阅库",
  robots: "index, follow",
  openGraph: {
    title: "三更订阅库 - 专业知识订阅服务平台",
    description: "三更订阅库提供经济学人、华尔街日报等全球顶级媒体的永久使用权订阅服务，充值独享账号，官网直接登录体验。",
    url: "https://sgtushu.com",
    siteName: "三更订阅库",
    locale: "zh_CN",
    type: "website",
    images: [
      {
        url: "https://sgtushu.com/sgtushu-image.jpeg",
        width: 1200,
        height: 630,
        alt: "三更订阅库 - 专业知识订阅服务平台",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "三更订阅库 - 专业知识订阅服务平台",
    description: "三更订阅库提供经济学人、华尔街日报等全球顶级媒体的永久使用权订阅服务",
    images: ["https://sgtushu.com/sgtushu-image.jpeg"],
  },
  alternates: {
    canonical: "https://sgtushu.com",
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#0d9488" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="三更订阅库" />
        <meta property="og:locale" content="zh_CN" />
        <meta property="og:site_name" content="三更订阅库" />

        <link rel="canonical" href="https://sgtushu.com" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
