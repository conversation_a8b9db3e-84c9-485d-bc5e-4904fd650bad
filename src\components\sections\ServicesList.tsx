'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardHeader, CardContent, CardFooter } from '../ui/Card';
import { ContactButton } from '../ui/ContactButton';

// 服务分类
const categories = [
  { id: 'all', name: '全部服务', count: 0 },
  { id: 'news', name: '新闻媒体', count: 0 },
  { id: 'finance', name: '财经商业', count: 0 },
  { id: 'academic', name: '学术期刊', count: 0 },
  { id: 'database', name: '数据库', count: 0 },
  { id: 'tech', name: '科技资讯', count: 0 },
];

// 服务数据
const services = [
  {
    id: 1,
    name: '经济学人 The Economist',
    category: 'news',
    description: '全球权威的政经周刊，深度分析国际时事和经济趋势。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥1100',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-economist-subscription-service.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: true
  },
  {
    id: 2,
    name: '华尔街日报 WSJ',
    category: 'finance',
    description: '美国最具影响力的财经报纸，提供最新的商业和金融资讯。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥380',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-wsj-wall-street-journal-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: false
  },
  {
    id: 3,
    name: '金融时报 FT',
    category: 'finance',
    description: '英国权威财经报纸，全球商业和金融领域的领导者。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥280',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-ft-financial-times-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: false
  },
  {
    id: 4,
    name: '日经亚洲 Nikkei Asia',
    category: 'finance',
    description: '日本经济新闻旗下英文媒体，专注亚洲商业和经济报道。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-nikkei-asia-subscription-service.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: false
  },
  {
    id: 5,
    name: '纽约时报 NYT',
    category: 'news',
    description: '美国权威报纸，提供全球新闻、深度报道和分析。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-nyt-new-york-times-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: false
  },
  {
    id: 6,
    name: '华盛顿邮报 Washington Post',
    category: 'news',
    description: '美国重要报纸，提供政治、社会和国际新闻报道。三更订阅库提供永久使用权，充值独享账号。',
    price: '¥400',
    period: '永久',
    originalPrice: '',
    image: '/images/services/sangengtushu-washington-post-subscription.png',
    features: ['永久使用权', '充值独享账号', '官网直接登录', '三更订阅库技术支持'],
    popular: false
  }
];

// 计算每个分类的服务数量
categories.forEach(category => {
  if (category.id === 'all') {
    category.count = services.length;
  } else {
    category.count = services.filter(service => service.category === category.id).length;
  }
});

export const ServicesList: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredServices, setFilteredServices] = useState(services);

  useEffect(() => {
    let filtered = services;

    // 按分类筛选
    if (activeCategory !== 'all') {
      filtered = filtered.filter(service => service.category === activeCategory);
    }

    // 按搜索词筛选
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredServices(filtered);
  }, [activeCategory, searchTerm]);

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400"
              >
                <svg className="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                  订阅服务
                </span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6 tracking-tight">
            三更订阅库订阅服务
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            三更订阅库为您提供全球顶级媒体、学术期刊和数据库的专业订阅服务，
            享受比官方更优惠的价格和更稳定的服务体验。
          </p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索订阅服务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 pl-12 pr-4 text-gray-900 dark:text-white bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl focus:outline-none"
              />
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar - Categories */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                服务分类
              </h3>
              <nav className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium ${
                      activeCategory === category.id
                        ? 'bg-teal-50 dark:bg-teal-900/20 text-teal-600 dark:text-teal-400'
                        : 'text-gray-600 dark:text-gray-300'
                    }`}
                  >
                    <span>{category.name}</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      activeCategory === category.id
                        ? 'bg-teal-100 dark:bg-teal-800 text-teal-600 dark:text-teal-300'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      {category.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content - Services Grid */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {searchTerm ? `搜索结果` : categories.find(c => c.id === activeCategory)?.name}
                <span className="text-gray-500 dark:text-gray-400 font-normal ml-2">
                  ({filteredServices.length} 个服务)
                </span>
              </h2>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="text-sm text-teal-600 dark:text-teal-400"
                >
                  清除搜索
                </button>
              )}
            </div>

            {filteredServices.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  没有找到相关服务
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm ? '请尝试其他搜索词' : '该分类暂无服务'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {filteredServices.map((service) => (
                <Card key={service.id} className="h-full flex flex-col">
                  {service.popular && (
                    <div className="absolute -top-3 left-4 z-10">
                      <span className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                        热门推荐
                      </span>
                    </div>
                  )}
                  
                  <CardHeader className="p-0">
                    <div className="relative w-full h-48 rounded-t-xl overflow-hidden">
                      <Image
                        src={service.image}
                        alt={`${service.name} - 三更订阅库订阅服务`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    </div>
                  </CardHeader>
                  
                  <CardContent className="flex-grow p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 tracking-tight">
                      {service.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 leading-relaxed">
                      {service.description}
                    </p>
                    
                    <div className="mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                          {service.price}
                        </span>
                      </div>
                      <div className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                        三更订阅库特惠价格
                      </div>
                    </div>
                    
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      {service.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-teal-600 rounded-full mr-3 flex-shrink-0"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  
                  <CardFooter className="p-6 pt-0">
                    <ContactButton variant="primary" className="w-full">
                      咨询客服
                    </ContactButton>
                  </CardFooter>
                </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
