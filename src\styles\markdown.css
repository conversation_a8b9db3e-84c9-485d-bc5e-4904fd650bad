/* Markdown Content Styles */
.markdown-content {
  @apply text-gray-700 dark:text-gray-300;
}

.markdown-content h1 {
  @apply text-4xl font-bold text-gray-900 dark:text-white mb-8 mt-0 leading-tight;
  @apply pb-4 border-b-2 border-gray-200 dark:border-gray-700;
}

.markdown-content h2 {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-6 mt-12 leading-tight;
  @apply relative pl-6 before:content-[''] before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-gradient-to-b before:from-teal-500 before:to-cyan-500 before:rounded-full;
  @apply bg-gradient-to-r from-gray-50 to-transparent dark:from-gray-800 dark:to-transparent py-3 pr-4 rounded-r-lg;
}

.markdown-content h3 {
  @apply text-2xl font-semibold text-teal-700 dark:text-teal-300 mb-4 mt-8 leading-tight;
  @apply relative pl-4 before:content-['▶'] before:absolute before:left-0 before:text-teal-500 before:text-lg before:font-bold;
}

.markdown-content h4 {
  @apply text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 mt-6 leading-tight;
  @apply border-l-4 border-teal-400 pl-4 bg-teal-50/50 dark:bg-teal-900/20 py-2 rounded-r;
}

.markdown-content h5 {
  @apply text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2 mt-4 leading-tight;
  @apply relative before:content-['●'] before:text-teal-500 before:mr-2 before:font-bold;
}

.markdown-content h6 {
  @apply text-base font-semibold text-gray-600 dark:text-gray-400 mb-2 mt-4 leading-tight;
  @apply uppercase tracking-wider text-sm border-b border-gray-300 dark:border-gray-600 pb-1 inline-block;
}

.markdown-content p {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed mb-6 text-lg;
}

.markdown-content a {
  @apply text-teal-600 dark:text-teal-400 no-underline hover:underline;
}

.markdown-content strong {
  @apply text-gray-900 dark:text-white font-semibold;
}

.markdown-content em {
  @apply text-gray-800 dark:text-gray-200 italic;
}

.markdown-content ul {
  @apply my-6 space-y-2 pl-6;
}

.markdown-content ol {
  @apply my-6 space-y-2 pl-6;
}

.markdown-content li {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed text-lg;
}

.markdown-content ul li {
  @apply list-disc;
}

.markdown-content ol li {
  @apply list-decimal;
}

.markdown-content blockquote {
  @apply border-l-4 border-teal-500 bg-teal-50 dark:bg-teal-900/20 py-4 px-6 my-8 rounded-r-lg text-gray-800 dark:text-gray-200;
}

.markdown-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-teal-700 dark:text-teal-300 font-mono text-sm;
}

.markdown-content pre {
  @apply bg-gray-900 dark:bg-gray-800 text-gray-100 rounded-lg p-6 overflow-x-auto my-8;
}

.markdown-content pre code {
  @apply bg-transparent px-0 py-0 text-gray-100;
}

.markdown-content table {
  @apply my-8 w-full border-collapse;
}

.markdown-content thead {
  @apply bg-gray-50 dark:bg-gray-800;
}

.markdown-content th {
  @apply px-4 py-3 text-left font-semibold text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700;
}

.markdown-content td {
  @apply px-4 py-3 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300;
}

.markdown-content hr {
  @apply border-gray-200 dark:border-gray-700 my-12;
}

.markdown-content img {
  @apply rounded-lg my-8 max-w-full h-auto mx-auto block;
  width: 80%; /* 缩小20% */
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.15),
    0 10px 20px -5px rgba(0, 0, 0, 0.1),
    0 4px 8px -2px rgba(0, 0, 0, 0.05);
}

.dark .markdown-content img {
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.4),
    0 10px 20px -5px rgba(0, 0, 0, 0.3),
    0 4px 8px -2px rgba(0, 0, 0, 0.2);
}

/* 图片说明文字样式 */
.markdown-content .image-caption {
  @apply text-center text-sm text-gray-600 dark:text-gray-400 mt-3 mb-6 italic;
}

/* 确保第一个标题没有上边距 */
.markdown-content > h1:first-child,
.markdown-content > h2:first-child,
.markdown-content > h3:first-child,
.markdown-content > h4:first-child,
.markdown-content > h5:first-child,
.markdown-content > h6:first-child {
  @apply mt-0;
}

/* 确保最后一个元素没有下边距 */
.markdown-content > *:last-child {
  @apply mb-0;
}
