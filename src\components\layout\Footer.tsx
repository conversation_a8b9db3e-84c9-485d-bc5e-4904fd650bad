import React from 'react';
import Link from 'next/link';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-1">
            <h3 className="text-xl font-bold mb-4 tracking-tight">三更订阅库</h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              三更订阅库致力于为用户提供最优质的知识订阅服务，包括经济学人、华尔街日报等顶级内容平台的永久使用权服务。
            </p>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">核心服务</h4>
            <ul className="space-y-3 text-gray-300">
              <li><span className="text-gray-300">经济学人订阅</span></li>
              <li><span className="text-gray-300">华尔街日报</span></li>
              <li><span className="text-gray-300">金融时报</span></li>
              <li><Link href="/services" className="text-gray-300 hover:text-white transition-colors">查看全部服务</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-4">联系我们</h4>
            <ul className="space-y-3 text-gray-300">
              <li>邮箱: <EMAIL></li>
              <li>网站: sgtushu.com</li>
              <li>三更订阅库技术支持</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
          <p className="text-sm">© 2024 三更订阅库 (sgtushu.com) | 安全可靠的订阅管理平台</p>
        </div>
      </div>
    </footer>
  );
};
