'use client';

import React, { useState } from 'react';
import { ContactModal } from './ContactModal';

interface ContactButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export const ContactButton: React.FC<ContactButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  children = '咨询客服'
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg focus:outline-none';

  const variantClasses = {
    primary: 'bg-teal-600 text-white',
    secondary: 'bg-gray-600 text-white',
    outline: 'border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300',
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={buttonClasses}
      >
        {children}
      </button>
      
      <ContactModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  );
};
