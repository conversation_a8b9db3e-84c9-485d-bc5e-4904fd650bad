import React from 'react';
import { Card, CardHeader, CardContent } from '../ui/Card';
import { ContactButton } from '../ui/ContactButton';

const serviceSteps = [
  {
    step: '01',
    title: '咨询客服',
    description: '联系三更订阅库专业客服，了解服务详情和价格信息',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    ),
    features: [
      '专业客服一对一服务',
      '详细价格和服务说明',
      '个性化需求分析',
      '24/7 在线咨询'
    ]
  },
  {
    step: '02',
    title: '确认订购',
    description: '选择您需要的媒体服务，确认订购信息和付款方式',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    features: [
      '灵活选择所需媒体',
      '透明价格无隐藏费用',
      '多种安全支付方式',
      '订单确认和跟踪'
    ]
  },
  {
    step: '03',
    title: '即时激活',
    description: '付款完成后立即为您开通账号，提供登录信息和使用指导',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    features: [
      '即时账号开通',
      '详细使用指导',
      '登录信息安全传递',
      '技术支持保障'
    ]
  }
];

export const Pricing: React.FC = () => {
  return (
    <section id="process" className="py-12 sm:py-16 lg:py-24 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6 tracking-tight">
            三更订阅库服务流程
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            简单三步即可享受三更订阅库专业的知识订阅服务，从咨询到使用全程无忧
          </p>
        </div>

        {/* Process Steps */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {serviceSteps.map((step, index) => (
            <Card key={index} className="relative h-full flex flex-col border border-gray-200 dark:border-gray-700">
              <CardHeader className="text-center p-8">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-white">
                    {step.icon}
                  </div>
                </div>
                <div className="text-sm font-bold text-teal-600 dark:text-teal-400 mb-2">
                  STEP {step.step}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3 tracking-tight">
                  {step.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  {step.description}
                </p>
              </CardHeader>

              <CardContent className="flex-grow p-8 pt-0">
                <ul className="space-y-3">
                  {step.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600 dark:text-gray-300 text-sm">
                      <span className="w-1.5 h-1.5 bg-teal-600 rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="mb-8">
            <ContactButton variant="primary" size="lg">
              立即咨询客服
            </ContactButton>
          </div>
          <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">
            三更订阅库专业团队为您提供全程服务，确保您的订阅体验安全可靠
          </p>
          <div className="flex flex-wrap justify-center gap-6 text-xs text-gray-400">
            <span>✓ 专业客服团队</span>
            <span>✓ 即时响应服务</span>
            <span>✓ 安全支付保障</span>
            <span>✓ 技术支持无忧</span>
          </div>
        </div>
      </div>
    </section>
  );
};
