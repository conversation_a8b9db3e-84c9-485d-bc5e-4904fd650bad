import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getArticleBySlug, getAllArticles, getRelatedArticles } from '@/lib/articles';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { ContactButton } from '@/components/ui/ContactButton';
import { QQContactButton } from '@/components/ui/QQContactButton';
import { MarkdownContent } from '@/components/ui/MarkdownContent';

interface ArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 生成静态路径
export async function generateStaticParams() {
  const articles = getAllArticles();
  return articles.map((article) => ({
    slug: article.slug,
  }));
}

// 生成元数据
export async function generateMetadata({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = await getArticleBySlug(slug);
  
  if (!article) {
    return {
      title: '文章未找到 - 三更订阅库',
    };
  }

  return {
    title: `${article.title} - 三更订阅库`,
    description: article.description,
    alternates: {
      canonical: `https://sgtushu.com/articles/${slug}`,
    },
    openGraph: {
      title: article.title,
      description: article.description,
      url: `https://sgtushu.com/articles/${slug}`,
      siteName: '三更订阅库',
      type: 'article',
      publishedTime: article.date,
      authors: [article.author || '三更订阅库'],
      tags: article.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.description,
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = await getArticleBySlug(slug);

  if (!article) {
    notFound();
  }

  const relatedArticles = getRelatedArticles(slug, 3);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Article Header */}
      <header className="bg-gradient-to-br from-gray-50 to-teal-50/30 dark:from-gray-800 dark:to-gray-700 py-8 sm:py-10 border-b-2 border-gray-200 dark:border-gray-600 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <nav className="flex mb-4" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link
                  href="/"
                  className="inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
                >
                  首页
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <Link
                    href="/articles"
                    className="ml-1 text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
                  >
                    文章中心
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 dark:text-gray-400">
                    {article.title}
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Article Meta */}
          <div className="mb-4">
            <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400 mb-3">
              <span className="bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 px-3 py-1 rounded-full">
                {article.category}
              </span>
              <span>{new Date(article.date).toLocaleDateString('zh-CN')}</span>
              <span>{article.readTime} 分钟阅读</span>
              {article.author && <span>作者：{article.author}</span>}
            </div>
            
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3 leading-tight">
              {article.title}
            </h1>

            <p className="text-base text-gray-600 dark:text-gray-300 leading-relaxed">
              {article.description}
            </p>
          </div>

          {/* Tags */}
          {article.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {article.tags.map(tag => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-full"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </header>

      {/* Article Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 bg-white dark:bg-gray-900">
        <article className="relative">
          {/* 装饰性分隔线 */}
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full"></div>
          <MarkdownContent content={article.content} />
        </article>

        {/* CTA Section */}
        <div className="mt-12 p-6 bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-gray-800 dark:to-gray-700 rounded-xl">
          <div className="text-center">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              对我们的服务感兴趣？
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              联系我们的专业客服，获取详细的订阅指导和优惠信息
            </p>
            <ContactButton variant="primary" size="lg">
              立即咨询
            </ContactButton>
          </div>
        </div>
      </main>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="bg-gray-50 dark:bg-gray-800 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
              相关文章
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedArticles.map(relatedArticle => (
                <Card key={relatedArticle.slug} className="h-full">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-teal-600 dark:text-teal-400 font-medium">
                        {relatedArticle.category}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {relatedArticle.readTime} 分钟
                      </span>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                      <Link 
                        href={`/articles/${relatedArticle.slug}`}
                        className="hover:text-teal-600 dark:hover:text-teal-400 transition-colors"
                      >
                        {relatedArticle.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3">
                      {relatedArticle.description}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(relatedArticle.date).toLocaleDateString('zh-CN')}
                    </span>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}
      <QQContactButton />
    </div>
  );
}
